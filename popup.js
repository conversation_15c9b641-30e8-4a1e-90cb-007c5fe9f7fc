// This script will handle the logic for the popup UI.
// - Fetching and displaying conversations
// - Handling search and filter events
// - Showing conversation details

let allConversations = []; // Cache all conversations
let currentView = 'list'; // 'list' or 'detail'
let currentPage = 1;
let currentSearch = '';
let isLoading = false;
let hasMorePages = true;
const PAGE_SIZE = 20;

document.addEventListener('DOMContentLoaded', () => {
  loadConversations();

  // Configure marked to use highlight.js for syntax highlighting
  // Configure marked to use highlight.js for syntax highlighting.
  // This is the recommended, robust way to integrate the two libraries.
  // Configure marked for GitHub Flavored Markdown.
  // We will handle highlighting separately after the content is in the DOM.
  marked.setOptions({
    gfm: true,
    breaks: true
  });

  const searchInput = document.getElementById('search-input');
  let searchTimeout;
  searchInput.addEventListener('input', (e) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      const searchTerm = e.target.value.toLowerCase();
      handleSearch(searchTerm);
    }, 300); // Debounce search for 300ms
  });

  document.getElementById('back-button').addEventListener('click', () => {
    showListView();
  });
});

function loadConversations(reset = true) {
  if (isLoading) return;

  if (reset) {
    currentPage = 1;
    allConversations = [];
    hasMorePages = true;
  }

  if (!hasMorePages) return;

  isLoading = true;
  showLoadingIndicator();

  const payload = {
    page: currentPage,
    limit: PAGE_SIZE,
    search: currentSearch
  };

  chrome.runtime.sendMessage({ namespace: 'database', action: 'getConversations', payload }, (response) => {
    isLoading = false;
    hideLoadingIndicator();

    if (chrome.runtime.lastError) {
      console.error("Error loading conversations:", chrome.runtime.lastError);
      return;
    }

    if (response && response.status === 'success') {
      const newConversations = response.data;
      const pagination = response.pagination;

      if (reset) {
        allConversations = newConversations;
      } else {
        allConversations = [...allConversations, ...newConversations];
      }

      hasMorePages = pagination.hasMore;
      currentPage = pagination.page + 1;

      renderConversations(allConversations, !reset);
      updateLoadMoreButton();
    } else {
      console.error("Error loading conversations:", response ? response.message : "No response");
    }
  });
}

function handleSearch(searchTerm) {
  currentSearch = searchTerm;
  loadConversations(true); // Reset and load with new search
}

function loadMoreConversations() {
  loadConversations(false); // Don't reset, append to existing
}

function renderConversations(conversations, append = false) {
  const listElement = document.getElementById('conversation-list');

  if (!append) {
    listElement.innerHTML = ''; // Clear existing list only if not appending
  }

  if (!conversations || conversations.length === 0) {
    if (!append) {
      listElement.innerHTML = '<p class="empty-message">No conversations recorded yet.</p>';
    }
    return;
  }

  // Remove empty message if it exists when appending
  if (append) {
    const emptyMessage = listElement.querySelector('.empty-message');
    if (emptyMessage) {
      emptyMessage.remove();
    }
  }

  conversations.forEach(conv => {
    // Skip if conversation already exists (prevent duplicates when appending)
    if (append && listElement.querySelector(`[data-conversation-id="${conv.id}"]`)) {
      return;
    }

    const item = document.createElement('div');
    item.className = 'conversation-item';
    item.setAttribute('data-conversation-id', conv.id);
    item.innerHTML = `
      <div class="item-header">
        <span class="platform-badge ${conv.platform.toLowerCase()}">${conv.platform}</span>
        <span class="item-title">${escapeHTML(conv.title)}</span>
        <span class="item-date">${new Date(conv.createdAt).toLocaleString()}</span>
      </div>
      <div class="item-preview">
        <strong>You:</strong> ${escapeHTML(conv.prompt.substring(0, 100))}...
      </div>
    `;
    item.addEventListener('click', () => {
      showDetailView(conv);
    });

    const menuButton = document.createElement('button');
    menuButton.className = 'menu-button';
    menuButton.innerHTML = '...';
    menuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      // We will implement a custom context menu here in a future step
      console.log("Menu button clicked for:", conv.id);
      if (confirm(`Are you sure you want to delete this conversation?\n\n"${conv.prompt.substring(0, 50)}..."`)) {
        deleteConversation(conv.id);
      }
    });

    item.querySelector('.item-header').appendChild(menuButton);

    // Insert before load more button if it exists, otherwise append to end
    const loadMoreButton = listElement.querySelector('.load-more-button');
    if (loadMoreButton) {
      listElement.insertBefore(item, loadMoreButton);
    } else {
      listElement.appendChild(item);
    }
  });
}

function showDetailView(conversation) {
  currentView = 'detail';
  document.getElementById('list-view').classList.add('hidden');
  const detailView = document.getElementById('detail-view');
  detailView.classList.remove('hidden');
  
  document.getElementById('detail-title').textContent = conversation.title;
  
  const detailElement = document.getElementById('conversation-detail');
  detailElement.innerHTML = `
    <div class="detail-section">
      <div class="detail-meta">
        <strong>Platform:</strong> ${conversation.platform} | 
        <strong>Date:</strong> ${new Date(conversation.createdAt).toLocaleString()} |
        <strong>URL:</strong> <a href="${conversation.url}" target="_blank">${escapeHTML(conversation.url)}</a>
      </div>
    </div>
    <div class="detail-section">
      <h2>Prompt</h2>
      <div class="detail-content">${escapeHTML(conversation.prompt)}</div>
    </div>
    <div class="detail-section">
      <h2>Response</h2>
      <div class="detail-content">${DOMPurify.sanitize(marked.parse(conversation.response || ''))}</div>
    </div>
  `;

  enhanceCodeBlocks(detailElement);
}

function enhanceCodeBlocks(container) {
  // Find all code blocks within the container.
  const codeElements = container.querySelectorAll('pre code');

  codeElements.forEach(codeEl => {
    // 1. Highlight the code block.
    // highlightElement will automatically detect the language.
    hljs.highlightElement(codeEl);

    // 2. Add a copy button to the parent <pre> element.
    const preEl = codeEl.parentElement;
    if (preEl.querySelector('.copy-button')) return; // Avoid adding duplicate buttons

    const button = document.createElement('button');
    button.className = 'copy-button';
    button.textContent = 'Copy';
    
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      navigator.clipboard.writeText(codeEl.innerText).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      }).catch(err => {
        console.error('Failed to copy code: ', err);
        button.textContent = 'Error';
      });
    });

    preEl.appendChild(button);
  });
}

function showListView() {
  currentView = 'list';
  document.getElementById('detail-view').classList.add('hidden');
  document.getElementById('list-view').classList.remove('hidden');
}

function showLoadingIndicator() {
  const listElement = document.getElementById('conversation-list');
  let loadingIndicator = listElement.querySelector('.loading-indicator');

  if (!loadingIndicator) {
    loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-indicator';
    loadingIndicator.innerHTML = '<p>Loading conversations...</p>';
    listElement.appendChild(loadingIndicator);
  }
}

function hideLoadingIndicator() {
  const listElement = document.getElementById('conversation-list');
  const loadingIndicator = listElement.querySelector('.loading-indicator');
  if (loadingIndicator) {
    loadingIndicator.remove();
  }

  // Reset load more button state
  const loadMoreButton = listElement.querySelector('.load-more-button');
  if (loadMoreButton) {
    loadMoreButton.innerHTML = '📚 Load More Conversations';
    loadMoreButton.disabled = false;
  }
}

function updateLoadMoreButton() {
  const listElement = document.getElementById('conversation-list');
  let loadMoreButton = listElement.querySelector('.load-more-button');
  let endMessage = listElement.querySelector('.end-message');

  if (hasMorePages && allConversations.length > 0) {
    // Show load more button
    if (!loadMoreButton) {
      loadMoreButton = document.createElement('button');
      loadMoreButton.className = 'load-more-button';
      loadMoreButton.innerHTML = '📚 Load More Conversations';
      loadMoreButton.addEventListener('click', () => {
        loadMoreButton.innerHTML = '⏳ Loading...';
        loadMoreButton.disabled = true;
        loadMoreConversations();
      });
      listElement.appendChild(loadMoreButton);
    }
    loadMoreButton.style.display = 'block';
    loadMoreButton.innerHTML = '📚 Load More Conversations';
    loadMoreButton.disabled = false;

    // Hide end message
    if (endMessage) {
      endMessage.style.display = 'none';
    }
  } else if (allConversations.length > 0) {
    // Hide load more button and show end message
    if (loadMoreButton) {
      loadMoreButton.style.display = 'none';
    }

    if (!endMessage) {
      endMessage = document.createElement('div');
      endMessage.className = 'end-message';
      endMessage.innerHTML = '🎉 You\'ve reached the end! All conversations loaded.';
      listElement.appendChild(endMessage);
    }
    endMessage.style.display = 'block';
  } else {
    // Hide both if no conversations
    if (loadMoreButton) {
      loadMoreButton.style.display = 'none';
    }
    if (endMessage) {
      endMessage.style.display = 'none';
    }
  }
}

function deleteConversation(id) {
  chrome.runtime.sendMessage({ namespace: 'database', action: 'deleteConversation', payload: { id: id } }, (response) => {
    if (response.status === 'success') {
      // Remove the conversation from the local cache
      allConversations = allConversations.filter(conv => conv.id !== id);

      // Remove the conversation item from the DOM
      const conversationItem = document.querySelector(`[data-conversation-id="${id}"]`);
      if (conversationItem) {
        conversationItem.remove();
      }

      // If we're in detail view for this conversation, go back to list
      if (currentView === 'detail') {
        showListView();
      }

      // If the list is now empty or we have very few items, try to load more
      if (allConversations.length < PAGE_SIZE / 2 && hasMorePages) {
        loadMoreConversations();
      }
    } else {
      console.error("Error deleting conversation:", response.message);
    }
  });
}

function escapeHTML(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}